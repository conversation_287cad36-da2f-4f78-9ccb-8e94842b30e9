{"name": "cultour-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:prod": "vite --mode production", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.0", "axios": "^1.11.0", "element-plus": "^2.11.2", "pinia": "^2.3.1", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.3.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.91.0", "typescript": "^5.6.3", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^6.0.0", "vue-tsc": "^3.0.6"}}