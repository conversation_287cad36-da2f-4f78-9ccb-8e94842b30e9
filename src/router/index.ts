import { createRouter, createWebHistory } from "vue-router";
import type { RouteRecordRaw } from "vue-router";
import Layout from "@/layout/index.vue";
import HomeView from "@/views/HomeView/index.vue";
import AboutView from "@/views/AboutView.vue";
import essentialRoutes from "./modules/essential";

const baseRoutes: Array<RouteRecordRaw> = [
  {
    path: "/home",
    name: "Home",
    meta: {
      title: "首页",
    },
    component: HomeView,
  },
  {
    path: "/about",
    name: "About",
    meta: {
      title: "关于",
    },
    component: AboutView,
  },
  {
    path: "/application",
    name: "Application",
    meta: {
      title: "申请",
    },
    component: () => import("@/views/ApplicationView.vue"),
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    meta: {
      title: "404",
    },
    component: () => import("@/views/DefaultView.vue"),
  },
];

// 定义路由列表
const routes: Array<RouteRecordRaw> = [
  ...baseRoutes,
  {
    path: "/",
    name: "main",
    component: Layout,
    redirect: "/home",
    children: [...essentialRoutes],
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    console.log(to, from, savedPosition);
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

// 全局路由守卫
router.beforeEach((to, from, next) => {
  console.log(to, from, next);
  // 设置页面标题
  // if (to.meta.title) {
  //   document.title = to.meta.title as string;
  // }
  next();
});

export default router;
