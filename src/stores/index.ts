// stores/index.ts
import { createPinia } from "pinia";

const pinia = createPinia();

// 全局插件
pinia.use(({ store }) => {
  console.log("store+++0", store);
  // 只处理 user store
  if (store.$id === "user") {
    const token = localStorage.getItem("AUTHORIZATION-CULTOUR-AI");
    console.log("store+++1", store);
    if (token) {
      console.log("store+++2", store);
      store.authorization = token;
      // 自动拉取用户信息
      store.fetchUserInfo?.();
    }
  }
});

export default pinia;
