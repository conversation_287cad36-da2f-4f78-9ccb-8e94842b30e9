// stores/user.ts
import { defineStore } from "pinia";
import { ref } from "vue";
import { getUser } from "@/api/user";

export const useUserStore = defineStore("user", () => {
  const name = ref<string>("");
  const orgId = ref<string>("");
  const authorization = ref<string>("");
  const loading = ref<boolean>(false);

  const isLoggedIn = () => !!authorization.value;

  function setAuthorization(token: string) {
    authorization.value = token;
    localStorage.setItem("AUTHORIZATION-CULTOUR-AI", token);
    fetchUserInfo();
  }

  function clearAuthorization() {
    authorization.value = "";
    localStorage.removeItem("AUTHORIZATION-CULTOUR-AI");
  }

  function setUserInfo(user: { name: string; orgId: string }) {
    name.value = user.name;
    orgId.value = user.orgId;
  }

  function clearUserInfo() {
    name.value = "";
    orgId.value = "";
  }

  async function fetchUserInfo() {
    if (!authorization.value) return;
    loading.value = true;
    try {
      const response = await getUser();
      console.log("获取用户信息成功", response);
      setUserInfo({
        name: response.data.name,
        orgId: response.data.orgId || "",
      });
    } catch (error) {
      console.warn("获取用户信息失败", error);
      clearAuthorization();
      clearUserInfo();
    } finally {
      console.log("获取用户信息完成");
      loading.value = false;
    }
  }

  function logout() {
    clearAuthorization();
    clearUserInfo();
  }

  return {
    name,
    orgId,
    authorization,
    loading,
    isLoggedIn,
    setAuthorization,
    setUserInfo,
    fetchUserInfo,
    clearUserInfo,
    logout,
  };
});
