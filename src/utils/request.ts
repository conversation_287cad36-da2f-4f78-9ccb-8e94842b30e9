// utils/request.ts
import axios from "axios";
import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
}  from "axios"
import { useUserStore } from "@/stores/user";
import type { ApiResponse } from "@/types/request";

// 创建实例
const request: AxiosInstance = axios.create({
  baseURL: "http://101.42.26.254:8099",
  timeout: 10000,
});

// 请求拦截器
request.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const userStore = useUserStore();
    const token =
      userStore.authorization || localStorage.getItem("AUTHORIZATION-CULTOUR-AI");

    if (token) {
      config.headers.set('token', `${token}`);
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const res = response.data;
    if (res.code !== 200) {
      return Promise.reject(res);
    }
    return response; // 返回完整的 AxiosResponse
  },
  (error) => {
    console.log("000000000000000000000000000000000");
    console.log(error);
    const userStore = useUserStore();
    if (error.response?.status === 401) {
      userStore.logout();
    }
    return Promise.reject(error);
  }
);

// 给 axios 实例加上泛型支持
function createRequest<T = any>(
  config: AxiosRequestConfig
): Promise<ApiResponse<T>> {
  return request(config).then(response => response.data) as Promise<ApiResponse<T>>;
}

// 挂载常用方法
export default {
  get<T = any>(url: string, config?: AxiosRequestConfig) {
    return createRequest<T>({ ...config, url, method: "get" });
  },
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig) {
    return createRequest<T>({ ...config, url, data, method: "post" });
  },
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig) {
    return createRequest<T>({ ...config, url, data, method: "put" });
  },
  delete<T = any>(url: string, config?: AxiosRequestConfig) {
    return createRequest<T>({ ...config, url, method: "delete" });
  },
};
