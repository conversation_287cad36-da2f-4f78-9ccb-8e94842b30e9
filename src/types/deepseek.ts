export interface ScenicInfo {
  companyName: string;
  basicInfo: string;
  visitorProfile: string;
  businessRevenue: string;
  marketing: string;
  serviceMeasures: string;
  atmosphere: string;
  competitors: string;
  currentChallenges: string;
  expectedGoals: string;
  otherRequirements: string;
}

export interface ChatMessage {
  role: "system" | "user" | "assistant";
  content: string;
}

export interface ChapterAnalysis {
  chapter: number;
  analysis: string; // 分析过程
  conclusion: string; // 最终结论
  timestamp: string;
  title: string;
  confirmed: boolean; // 用户是否确认该章节
  feedback?: string; // 用户反馈意见
  regenerationCount: number; // 重新生成次数
  isGenerating?: boolean; // 是否正在生成
}

export interface ReportGenerationState {
  isGenerating: boolean;
  currentChapter: number;
  totalChapters: number;
  progress: number;
  messages: ChatMessage[];
  reportData: ChapterAnalysis[];
}

export interface DeepSeekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface StreamChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    delta: {
      role?: string;
      content?: string;
    };
    finish_reason: string | null;
  }>;
}
