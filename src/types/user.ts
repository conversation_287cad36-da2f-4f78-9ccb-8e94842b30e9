export interface loginInfo {
  username: string;
  password: string;
}

export interface registerInfo extends loginInfo {
  passwordCheck: string;
}

export interface checkTips {
  usernameTip: string;
  passwordTip: string;
  passwordCheckTip: string;
}

// API 响应类型定义
export interface UserInfo {
  id: string;
  username: string;
  name: string;
  orgId: string;
  email?: string;
  phone?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginResponse {
  token: string;
  user: UserInfo;
  expiresIn: number;
}

export interface RegisterResponse {
  message: string;
  user: Omit<UserInfo, 'id' | 'createdAt' | 'updatedAt'>;
}
