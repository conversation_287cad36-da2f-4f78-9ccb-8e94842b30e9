@use "./element-theme.scss" as *;
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary: #00ced1; /* 青色 */
  --primary-light: #5cede1; /* 浅青色 */
  --primary-dark: #009999; /* 深青色 */
  --secondary: #2f4f4f; /* 深灰色 */
  --accent: #20b2aa; /* 青绿色 */
  --light: #ffffff;
  --light-bg: #f5f8fa; /* 浅灰蓝色背景 */
  --dark-text: #2c3e50; /* 深灰色文字 */
  --gray: #708090; /* 石板灰 */
  --light-gray: #d1d5db; /* 浅灰色 */
  --card-bg: rgba(255, 255, 255, 0.85);
  --gradient: linear-gradient(135deg, var(--primary), var(--accent));
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --glass: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
}

body {
  font-family: "Noto Sans SC", sans-serif;
  background: var(--light-bg);
  color: var(--dark-text);
  line-height: 1.6;
  overflow-x: hidden;
  background-image: linear-gradient(
      rgba(245, 248, 250, 0.95),
      rgba(245, 248, 250, 0.95)
    ),
    url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%2300CED1" stroke-width="0.3" stroke-dasharray="5 5" opacity="0.1"/></svg>');
}

.main-container {
  padding: 32px;
}

.main-layout-container {
  max-width: 1200px;
  margin: 0 auto;
}
