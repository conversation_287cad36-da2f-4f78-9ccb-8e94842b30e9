<template>
  <div class="default-page">
    <div class="main">
      <h1>系统功能正在开发中...</h1>
      <h3>敬请期待</h3>
      <a href="/home">返回主页</a>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.default-page {
  height: 100vh;
  background-image: url("@/assets/default-page-bg.png");
  background-size: cover;
  position: relative;
  .main {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #fff;
    > a {
      color: #fff;
    }
  }
}
</style>
