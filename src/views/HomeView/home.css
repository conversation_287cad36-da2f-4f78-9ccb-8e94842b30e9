.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Header Styles */
header {
  padding: 1.2rem 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  backdrop-filter: blur(10px);
  background: rgba(245, 248, 250, 0.9);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: var(--gradient);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

.logo-icon i {
  font-size: 1.5rem;
  color: white;
  position: relative;
  z-index: 1;
}

.logo-text {
  font-family: "Raleway", sans-serif;
  font-weight: 800;
  font-size: 1.8rem;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

nav ul {
  display: flex;
  gap: 2rem;
  list-style: none;
}

nav a {
  color: var(--dark-text);
  text-decoration: none;
  font-weight: 500;
  font-size: 1.1rem;
  transition: color 0.3s;
  position: relative;
}

nav a:hover {
  color: var(--primary);
}

nav a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient);
  transition: width 0.3s;
}

nav a:hover::after {
  width: 100%;
}

.cta-button {
  background: var(--gradient);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 4px 15px rgba(0, 206, 209, 0.3);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.cta-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
  z-index: -1;
}

.cta-button:hover::before {
  transform: translateX(100%);
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 206, 209, 0.4);
}

/* Homepage Hero */
.home-hero {
  height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 80px;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: radial-gradient(
      circle at 10% 20%,
      rgba(0, 206, 209, 0.1) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 90% 80%,
      rgba(32, 178, 170, 0.1) 0%,
      transparent 40%
    );
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  position: relative;
}

.hero-text {
  max-width: 600px;
}

.hero-title {
  font-family: "Raleway", sans-serif;
  font-weight: 800;
  font-size: 3.5rem;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: var(--secondary);
}

.hero-title span {
  background: linear-gradient(to right, var(--primary), var(--accent));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--gray);
  margin-bottom: 2rem;
  line-height: 1.8;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
}

.secondary-button {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
  padding: 0.75rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.secondary-button:hover {
  background: rgba(0, 206, 209, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 206, 209, 0.1);
}

.hero-image {
  position: relative;
  display: flex;
  justify-content: center;
}

.dashboard-preview {
  width: 100%;
  max-width: 500px;
  height: 400px;
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 206, 209, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  transition: var(--transition);
}

.dashboard-preview::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="none" stroke="%2300CED1" stroke-width="0.3" stroke-dasharray="5 5" opacity="0.1"/></svg>');
  z-index: -1;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 12px;
  width: 100%;
  height: 100%;
}

.grid-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  border: 1px solid var(--light-gray);
  display: flex;
  flex-direction: column;
  padding: 15px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
}

.grid-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 206, 209, 0.15);
}

.grid-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--gradient);
}

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.grid-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--dark-text);
}

.grid-icon {
  width: 30px;
  height: 30px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 206, 209, 0.1);
}

.grid-icon i {
  font-size: 0.9rem;
  color: var(--primary);
}

/* Features Section */
.features-section {
  padding: 8rem 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: 5rem;
}

.section-title {
  font-family: "Raleway", sans-serif;
  font-weight: 800;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.section-subtitle {
  color: var(--gray);
  font-size: 1.25rem;
  max-width: 700px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
}

.feature-card {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--light-gray);
  backdrop-filter: blur(5px);
}

.feature-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 40px rgba(0, 206, 209, 0.2);
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: var(--gradient);
}

.feature-icon {
  width: 70px;
  height: 70px;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  background: rgba(0, 206, 209, 0.1);
  font-size: 1.8rem;
  color: var(--primary);
  position: relative;
  overflow: hidden;
}

.feature-icon::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

.feature-icon i {
  position: relative;
  z-index: 1;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--dark-text);
}

.feature-desc {
  color: var(--gray);
  margin-bottom: 1.5rem;
  line-height: 1.8;
}

/* User Dashboard */
.dashboard-section {
  display: none;
  padding: 2rem 0;
  min-height: 100vh;
}

.user-dashboard {
  display: grid;
  grid-template-columns: 260px 1fr;
  gap: 1.5rem;
  padding-top: 100px;
}

.sidebar {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  height: fit-content;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 206, 209, 0.1);
}

.nav-section {
  margin-bottom: 2rem;
}

.nav-title {
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--gray);
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.nav-links {
  list-style: none;
}

.nav-links li {
  margin-bottom: 0.3rem;
}

.nav-links a {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.85rem 1rem;
  color: var(--dark-text);
  text-decoration: none;
  border-radius: 10px;
  transition: var(--transition);
  position: relative;
}

.nav-links a:hover {
  background: rgba(0, 206, 209, 0.08);
}

.nav-links a.active {
  background: rgba(0, 206, 209, 0.1);
  color: var(--primary);
  font-weight: 500;
}

.nav-links a i {
  width: 24px;
  text-align: center;
  font-size: 1.1rem;
}

.main-content {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 206, 209, 0.1);
}

.welcome-banner {
  background: linear-gradient(
    135deg,
    rgba(0, 206, 209, 0.08),
    rgba(32, 178, 170, 0.08)
  );
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.welcome-title {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--dark-text);
}

.welcome-subtitle {
  color: var(--gray);
  margin-bottom: 1.5rem;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--card-bg);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 206, 209, 0.1);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 206, 209, 0.15);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  background: rgba(0, 206, 209, 0.1);
  font-size: 1.4rem;
  color: var(--primary);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
  color: var(--dark-text);
}

.stat-label {
  color: var(--gray);
  font-size: 0.9rem;
}

.dashboard-modules {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.module-card {
  background: var(--card-bg);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: var(--transition);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 206, 209, 0.1);
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 206, 209, 0.15);
}

.module-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.module-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 206, 209, 0.1);
  font-size: 1.4rem;
  color: var(--primary);
}

.module-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-text);
}

.module-desc {
  color: var(--gray);
  margin-bottom: 1.5rem;
}

/* Planning Page */
.planning-page {
  display: none;
  padding: 2rem 0;
  min-height: 100vh;
}

.planning-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 1.5rem;
  padding-top: 100px;
}

.planning-sidebar {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  height: fit-content;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 206, 209, 0.1);
}

.planning-content {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--shadow);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 206, 209, 0.1);
}

.planning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--light-gray);
}

.planning-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--dark-text);
}

.planning-subtitle {
  color: var(--gray);
  font-size: 1rem;
}

.planning-steps {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.step {
  flex: 1;
  text-align: center;
  padding: 1rem;
  border-radius: 10px;
  background: rgba(0, 206, 209, 0.05);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.step::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background: var(--gradient);
  opacity: 0.1;
  transition: height 0.3s;
  z-index: -1;
}

.step:hover::before {
  height: 100%;
}

.step.active {
  background: var(--primary);
  color: white;
}

.step.active::before {
  display: none;
}

.planning-form {
  margin-top: 2rem;
}

.form-section {
  margin-bottom: 2.5rem;
}

.section-title {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--dark-text);
  position: relative;
  padding-left: 1rem;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 70%;
  background: var(--primary);
  border-radius: 2px;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-text);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.9rem 1.2rem;
  border-radius: 12px;
  border: 1px solid var(--light-gray);
  background: var(--light-bg);
  color: var(--dark-text);
  font-size: 1rem;
  transition: var(--transition);
  font-family: "Noto Sans SC", sans-serif;
}

.form-group textarea {
  min-height: 150px;
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(0, 206, 209, 0.2);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--light-gray);
}

/* Footer */
footer {
  padding: 5rem 0 3rem;
  background: var(--card-bg);
  border-top: 1px solid var(--light-gray);
  position: relative;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
}

.footer-column h3 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--dark-text);
  position: relative;
  display: inline-block;
}

.footer-column h3::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--gradient);
  border-radius: 3px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-links a {
  color: var(--gray);
  text-decoration: none;
  transition: color 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-links a:hover {
  color: var(--primary);
}

.footer-links a i {
  font-size: 0.8rem;
}

.copyright {
  text-align: center;
  padding-top: 3rem;
  color: var(--gray);
  font-size: 0.9rem;
}

/* Login Form */
.login-form-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease;
}

.login-form-container.active {
  opacity: 1;
  visibility: visible;
}

.login-form {
  background: var(--card-bg);
  border-radius: 25px;
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(30px);
  transition: transform 0.4s ease;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 206, 209, 0.2);
}

.login-form-container.active .login-form {
  transform: translateY(0);
}

.close-login {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--light-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.close-login:hover {
  background: var(--primary);
  color: white;
}

.login-title {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
  color: var(--dark-text);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-text);
}

.form-group input {
  width: 100%;
  padding: 0.9rem 1.2rem;
  border-radius: 12px;
  border: 1px solid var(--light-gray);
  background: var(--light-bg);
  color: var(--dark-text);
  font-size: 1rem;
  transition: var(--transition);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(0, 206, 209, 0.2);
}

.login-button {
  width: 100%;
  padding: 1rem;
  background: var(--gradient);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.login-button:hover::before {
  transform: translateX(100%);
}

.login-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 206, 209, 0.3);
}

.form-footer {
  text-align: center;
  margin-top: 1.5rem;
  color: var(--gray);
}

.form-footer a {
  color: var(--primary);
  text-decoration: none;
}

/* Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes shine {
  0% {
    transform: rotate(45deg) translateX(-100%);
  }
  100% {
    transform: rotate(45deg) translateX(100%);
  }
}

.floating {
  animation: float 6s ease-in-out infinite;
}

.delay-1 {
  animation-delay: 0.2s;
}

.delay-2 {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 206, 209, 0.5);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(0, 206, 209, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 206, 209, 0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Responsive */
@media (max-width: 992px) {
  .hero-content {
    grid-template-columns: 1fr;
  }

  .hero-text {
    text-align: center;
    max-width: 100%;
  }

  .hero-buttons {
    justify-content: center;
  }

  .hero-image {
    justify-content: center;
  }

  .hero-title {
    font-size: 2.8rem;
  }

  .user-dashboard,
  .planning-container {
    grid-template-columns: 1fr;
  }

  .sidebar,
  .planning-sidebar {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  nav {
    display: none;
  }

  .hero-title {
    font-size: 2.3rem;
  }

  .section-title {
    font-size: 2rem;
  }
}
