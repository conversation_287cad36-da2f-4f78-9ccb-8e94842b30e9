<template>
  <div class="login-form-container" id="login-container" ref="loginContainer">
    <div class="login-form">
      <div class="close-login" id="close-login" @click="closeLoginContainer">
        <i class="fas fa-times"></i>
      </div>
      <div
        v-show="isLoginContainerActive"
        :class="{isLoginContainerActive?'login-form-transition-open':'login-form-transition-close'}"
      >
        <h2 class="login-title">欢迎登录文旅大师</h2>
        <div class="form-group">
          <label for="username">用户名</label>
          <input type="text" id="username" placeholder="请输入用户名" />
        </div>
        <div class="form-group">
          <label for="password">密码</label>
          <input type="password" id="password" placeholder="请输入密码" />
        </div>
        <button class="login-button" id="login-submit">登录系统</button>
        <div class="form-footer">
          没有账户？
          <a href="#" @click.prevent="isLoginContainerActive = false"
            >立即注册</a
          >
        </div>
      </div>
      <div
        v-show="!isLoginContainerActive"
        :class="{isLoginContainerActive?'register-form-transition-close':'register-form-transition-open'}"
      >
        <h2 class="login-title">用户注册</h2>
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            type="text"
            id="register-username"
            placeholder="请输入用户名"
          />
        </div>
        <div class="form-group">
          <label for="password">密码</label>
          <input
            type="password"
            id="register-password"
            placeholder="请输入密码"
          />
        </div>
        <div class="form-group">
          <label for="password">确认密码</label>
          <input
            type="password"
            id="register-password"
            placeholder="请输入密码"
          />
        </div>
        <button class="login-button" id="register-submit">立即注册</button>
        <div class="form-footer">
          已有账号？
          <a href="#" @click.prevent="isLoginContainerActive = true"
            >返回登录</a
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
const isLoginContainerActive = ref(true);
const loginContainer = ref<HTMLElement | null>(null);
const showLoginContainer = () => {
  loginContainer.value?.classList.add("active");
};
const closeLoginContainer = () => {
  loginContainer.value?.classList.remove("active");
};
defineExpose({
  showLoginContainer,
  closeLoginContainer,
});
</script>
<style src="./home.css" scoped></style>
<style lang="scss" scoped>
/* 炫酷动画过渡效果 */
.login-form-transition-open,
.login-form-transition-close,
.register-form-transition-open,
.register-form-transition-close {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}
.login-form-transition-open,.register-form-transition-open {
  max-width: unset;
}
.login-form-transition-close,.register-form-transition-close{
    max-width: 0px;
}
</style>
