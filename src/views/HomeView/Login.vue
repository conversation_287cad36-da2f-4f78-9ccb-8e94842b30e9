<template>
  <div class="login-form-container" id="login-container" ref="loginContainer">
    <div class="login-form">
      <div class="close-login" id="close-login" @click="closeLoginContainer">
        <i class="fas fa-times"></i>
      </div>
      <div v-if="isLoginContainerActive">
        <h2 class="login-title">欢迎登录文旅大师</h2>
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            type="text"
            id="username"
            placeholder="请输入用户名"
            v-model="loginUserInfo.username"
          />
          <span class="check-tip">{{ checkTips.usernameTip }}</span>
        </div>
        <div class="form-group">
          <label for="password">密码</label>
          <input
            type="password"
            id="password"
            placeholder="请输入密码"
            v-model="loginUserInfo.password"
          />
          <span class="check-tip">{{ checkTips.passwordTip }}</span>
        </div>
        <button class="login-button" id="login-submit" @click="toLogin">
          登录系统
        </button>
        <div class="form-footer">
          没有账户？
          <a href="#" @click.prevent="changeStatus">立即注册</a>
        </div>
      </div>
      <div v-else>
        <h2 class="login-title">用户注册</h2>
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            type="text"
            id="register-username"
            placeholder="请输入用户名"
            v-model="registerUserInfo.username"
          />
          <span class="check-tip">{{ checkTips.usernameTip }}</span>
        </div>
        <div class="form-group">
          <label for="password">密码</label>
          <input
            type="password"
            id="register-password"
            placeholder="请输入密码"
            v-model="registerUserInfo.password"
          />
          <span class="check-tip">{{ checkTips.passwordTip }}</span>
        </div>
        <div class="form-group">
          <label for="password">确认密码</label>
          <input
            type="password"
            id="register-password-check"
            placeholder="请输入密码"
            v-model="registerUserInfo.passwordCheck"
          />
          <span class="check-tip">{{ checkTips.passwordCheckTip }}</span>
        </div>
        <button class="login-button" id="register-submit" @click="toRegister">
          立即注册
        </button>
        <div class="form-footer">
          已有账号？
          <a href="#" @click.prevent="changeStatus">返回登录</a>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { clientUserRegister, clientUserLogin } from "@/api/user";
import type { loginInfo, registerInfo, checkTips } from "@/types/user";
import { ref, watch } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
const userStore = useUserStore();
const isLoginContainerActive = ref(true);
const loginContainer = ref<HTMLElement | null>(null);
const loginUserInfo = ref<loginInfo>({
  username: "",
  password: "",
});
const registerUserInfo = ref<registerInfo>({
  username: "",
  password: "",
  passwordCheck: "",
});
const checkTips = ref<checkTips>({
  usernameTip: "",
  passwordTip: "",
  passwordCheckTip: "",
});
const loginUserInfoCheck = ({ username, password }: loginInfo) => {
  if (username == "") {
    checkTips.value.usernameTip = "用户名不能为空";
  } else {
    checkTips.value.usernameTip = "";
  }

  if (password == "") {
    checkTips.value.passwordTip = "密码不能为空";
  } else {
    checkTips.value.passwordTip = "";
  }
};
// watch(
//   loginUserInfo,
//   (newValue) => {
//     loginUserInfoCheck({ ...newValue });
//   },
//   { deep: true }
// );
const registerUserInfoCheck = ({
  username,
  password,
  passwordCheck,
}: registerInfo) => {
  if (username == "") {
    checkTips.value.usernameTip = "用户名不能为空";
  } else {
    checkTips.value.usernameTip = "";
  }

  if (password == "") {
    checkTips.value.passwordTip = "密码不能为空";
  } else {
    checkTips.value.passwordTip = "";
  }

  if (passwordCheck == "") {
    checkTips.value.passwordCheckTip = "密码不能为空";
  } else if (password !== passwordCheck) {
    checkTips.value.passwordCheckTip = "两次输入的密码不一致";
  } else {
    checkTips.value.passwordCheckTip = "";
  }
};
// watch(
//   registerUserInfo,
//   (newValue) => {
//     registerUserInfoCheck({ ...newValue });
//   },
//   { deep: true }
// );
const showLoginContainer = () => {
  clearCheckTips();
  loginContainer.value?.classList.add("active");
};
const closeLoginContainer = () => {
  loginContainer.value?.classList.remove("active");
};
const router = useRouter();
const toLogin = () => {
  loginUserInfoCheck(loginUserInfo.value);
  if (isObjectEmpty(checkTips.value)) {
    clientUserLogin(loginUserInfo.value)
      .then((res) => {
        if (res.code == 200) {
          ElMessage({
            type: "success",
            message: res.message,
          });
          // localStorage.setItem("AUTHORIZATION-CULTOUR-AI", res.data.token);
          userStore.setAuthorization(res.data.token);
          router.push("/Cultour-AI");
        } else {
          ElMessage({
            type: "warning",
            message: res.message,
          });
        }
      })
      .catch((err) => {
        console.warn(err);
        checkTips.value.usernameTip = "用户名或密码错误";
      });
  }
};
const toRegister = async () => {
  registerUserInfoCheck(registerUserInfo.value);
  if (isObjectEmpty(checkTips.value)) {
    clientUserRegister(registerUserInfo.value)
      .then((res) => {
        ElMessage({
          type: "success",
          message: res.message,
        });
      })
      .catch((err) => {
        if (err.code == 201) {
          checkTips.value.usernameTip = err.message;
        } else {
          ElMessage({
            type: "success",
            message: err.message,
          });
        }
      });
  }
};
function isObjectEmpty(obj: Record<string, any>): boolean {
  return Object.values(obj).every(
    (value) => value === null || value === undefined || value === ""
  );
}
const clearCheckTips = () => {
  checkTips.value = {
    usernameTip: "",
    passwordTip: "",
    passwordCheckTip: "",
  };
};
const changeStatus = () => {
  clearCheckTips();
  isLoginContainerActive.value = !isLoginContainerActive.value;
};
defineExpose({
  showLoginContainer,
  closeLoginContainer,
});
</script>
<style src="./home.css" scoped></style>
<style lang="scss" scoped>
.form-group {
  position: relative;
  .check-tip {
    position: absolute;
    bottom: -1.3rem;
    left: 1.2rem;
    color: red;
    font-size: 0.8rem;
  }
}
</style>
