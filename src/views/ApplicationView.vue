<template>
  <div class="application-page">
    <!-- Header -->
    <header>
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <div class="logo-text">文旅大师</div>
          </div>
          <span>内测申请入口</span>
        </div>
      </div>
    </header>
    <div class="application-warpper">
      <div class="tip-module">
        <el-icon><InfoFilled /></el-icon
        >欢迎申请文旅大师内测资格，填写以下信息后，我们将尽快审核您的申请
      </div>
      <div class="info-module">
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-position="top"
          label-width="auto"
        >
          <el-form-item label="姓名" prop="realName">
            <el-input v-model="ruleForm.realName" />
          </el-form-item>
          <el-form-item label="联系方式" prop="mobile">
            <el-input v-model="ruleForm.mobile" />
          </el-form-item>
          <el-form-item label="所在景区" prop="scenicName">
            <el-input v-model="ruleForm.scenicName" />
          </el-form-item>
          <el-form-item label="岗位" prop="postName">
            <el-input v-model="ruleForm.postName" />
          </el-form-item>
          <el-form-item label="其他信息" prop="otherInfo">
            <el-input
              type="textarea"
              :rows="3"
              placeholder="请填写您想补充的信息（选填）"
              maxlength="50"
              show-word-limit
              v-model="ruleForm.otherInfo"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm(ruleFormRef)">
              提交申请
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { InfoFilled } from "@element-plus/icons-vue";
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { saveScenicApplyInfo } from "@/api/user";

interface RuleForm {
  realName: string;
  scenicName: string;
  postName: string;
  otherInfo: string;
  mobile: string;
}

const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
  realName: "",
  mobile: "",
  scenicName: "",
  postName: "",
  otherInfo: "",
});

const rules = reactive<FormRules<RuleForm>>({
  realName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  mobile: [{ required: true, message: "请输入您的联系方式", trigger: "blur" }],
  scenicName: [
    { required: true, message: "请输入您所在的景区", trigger: "blur" },
  ],
  postName: [{ required: true, message: "请输入您的岗位", trigger: "blur" }],
});

let request_clock = false;
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      //   console.log("submit!");
      if (!request_clock) {
        request_clock = true;
        toRequest();
      } else {
        ElMessage({
          type: "warning",
          message: "申请太频繁了，请稍后再试！",
        });
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};

const toRequest = () => {
  saveScenicApplyInfo(ruleForm)
    .then((res) => {
      console.log(res);
      if (res.code == 200) {
        ElMessage({
          type: "success",
          message: "申请成功，我们会尽快与您联系！",
        });
      } else {
        ElMessage({
          type: "warning",
          message: res.message,
        });
      }
    })
    .catch((err) => {
      console.log(err);
      ElMessage({
        type: "error",
        message: err.message,
      });
    })
    .finally(() => {
      setTimeout(() => {
        request_clock = false;
      }, 60000);
    });
};
</script>

<style src="./HomeView/home.css" scoped></style>
<style lang="scss" scoped>
.application-page {
  padding-bottom: 48px;
  header {
    position: relative;
    .header-content {
      display: block;
      text-align: center;
      .logo {
        justify-content: center;
      }
    }
  }

  .application-warpper {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    .tip-module {
      margin: 20px 0;
      padding: 8px 16px;
      border-left: 5px solid var(--primary);
      background-color: #5cede1;
      border-radius: 4px;
    }
    .info-module {
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      background: #fff;
      .el-form {
        .el-form-item {
          .el-form-item__content {
            .el-button {
              //   --el-color-primary: #00ced1;
              //   ---el-color-primary-light-3: #00ced1;
              width: 100%;
              margin: 0 auto;
            }
          }
        }
      }
    }
  }
  @media screen and (max-width: 768px) {
    padding-bottom: 24px;
    .application-warpper {
      margin: 0 15px;
      width: calc(100% - 30px);
      .tip-module {
        margin: 12px 0;
        padding: 6px 12px;
      }
      .info-module{
        padding: 15px;
        .el-form {
        .el-form-item {
          margin-bottom: 12px;
        }
      }
      }
    }
  }
}
</style>
