<template>
  <div class="main">
    <div class="sidebar">
      <NavBar />
    </div>
    <div class="main-app">
      <router-view></router-view>
    </div>
  </div>
</template>
<script lang="ts" setup>
import NavBar from "./components/NavBar.vue";
</script>
<style lang="scss" scoped>
.main {
  width: 100vw;
  height: 100vh;
  display: flex;
  background-color: #f5f8fa;
  .sidebar {
    height: 100%;
    background: #f5f8fa;
    float: left;
  }
  .main-app {
    flex: 1;
    height: calc(100% - 48px);
    float: left;
    margin: 24px;
    background: #fff;
    overflow: hidden;
  }
}
</style>
