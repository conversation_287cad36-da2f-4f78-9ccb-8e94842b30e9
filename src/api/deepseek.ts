import type { ChatMessage, StreamChunk } from "@/types/deepseek";

const DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions";
const DEEPSEEK_MODEL = "deepseek-reasoner";
const API_KEY = "***********************************";

// 流式API调用
export const callDeepSeekAPIStream = async (
  messages: ChatMessage[],
  onChunk: (content: string) => void,
  onComplete: () => void,
  onError: (error: Error) => void
): Promise<void> => {
  try {
    // 转换消息格式为DeepSeek API所需的格式
    const apiMessages = messages.map((msg) => ({
      role: msg.role,
      content: msg.content,
    }));

    const response = await fetch(DEEPSEEK_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${API_KEY}`,
      },
      body: JSON.stringify({
        model: DEEPSEEK_MODEL,
        messages: apiMessages,
        temperature: 0,
        stream: true,
      }),
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    if (!response.body) {
      throw new Error("响应体不可用");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";
    let isConclusionSection = false;

    while (true) {
      const { value, done } = await reader.read();

      if (done) {
        onComplete();
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || ""; // 保存未完成的行

      for (const line of lines) {
        if (line.startsWith("data: ") && line !== "data: [DONE]") {
          try {
            const data = JSON.parse(line.substring(6)) as StreamChunk;

            if (
              data.choices &&
              data.choices.length > 0 &&
              data.choices[0].delta?.content
            ) {
              const content = data.choices[0].delta.content;
              onChunk(content);
            }
          } catch (e) {
            console.error("解析流数据时出错:", e, line);
          }
        }
      }
    }
  } catch (error) {
    console.error("调用DeepSeek API流时出错:", error);
    onError(error as Error);
  }
};
