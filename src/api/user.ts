import request from "@/utils/request";
import type {
  loginInfo,
  registerInfo,
  UserInfo,
  LoginResponse,
  RegisterResponse
} from "@/types/user";
import type { ApiResponse } from "@/types/request";

/**
 * 用户注册
 * @param data 注册信息
 * @returns Promise<ApiResponse<RegisterResponse>>
 */
export function clientUserRegister(data: registerInfo): Promise<ApiResponse<RegisterResponse>> {
  return request.post<RegisterResponse>("/api/index/clientUserRegister", data);
}

/**
 * 用户登录
 * @param data 登录信息
 * @returns Promise<ApiResponse<LoginResponse>>
 */
export function clientUserLogin(data: loginInfo): Promise<ApiResponse<LoginResponse>> {
  return request.post<LoginResponse>("/api/index/clientUserLogin", data);
}

/**
 * 获取当前用户信息
 * @returns Promise<ApiResponse<UserInfo>>
 */
export function getUser(): Promise<ApiResponse<UserInfo>> {
  return request.get<UserInfo>("/api/index/clientUserInfo");
}

/**
 * 文旅大师内测申请
*/
export function saveScenicApplyInfo(data: any): Promise<ApiResponse<any>> {
  return request.post<any>("/api/scenicApply/saveScenicApplyInfo", data);
}
