import request from "@/utils/request";
import type { loginInfo, registerInfo } from "@/types/user";

export function clientUserRegister(data: registerInfo) {
  return request({
    url: "/api/index/clientUserRegister",
    method: "post",
    data,
  });
}

export function clientUserLogin(data: loginInfo) {
  return request({
    url: "/api/index/clientUserLogin",
    method: "post",
    data,
  });
}

export function getUser() {
  return request({
    url: "/user",
    method: "get",
  });
}
