import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(process.cwd(), "src"),
    },
  },
  // server: {
  //   host: "0.0.0.0", // 局域网可访问
  //   port: 5173, // 自定义端口
  //   open: true, // 自动打开浏览器
  //   proxy: {
  //     // 代理配置
  //     "/api": {
  //       target: "http://localhost:3000", // 你的后端服务地址
  //       changeOrigin: true, // 修改请求头 host
  //       rewrite: (path) => path.replace(/^\/api/, ""), // 去掉 /api 前缀
  //     },
  //   },
  // },
});
